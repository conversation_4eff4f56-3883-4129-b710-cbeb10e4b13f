import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { MainStackParamList } from '../../types/navigation';
import { useTheme } from '../../theme/ThemeContext';
import { applyThemeShadow } from '../../utils/styleUtils';
import { Text, SafeAreaContainer } from '../../components/ui';
import PixelAvatarV2 from '../../components/avatar/PixelAvatarV2';

type CustomAvatarScreenNavigationProp = NativeStackNavigationProp<MainStackParamList>;

/**
 * Screen for skribbl.io style pixel avatar customization
 */
const CustomAvatarScreen = () => {
  const { theme, typography, spacing, borderRadius } = useTheme();
  const navigation = useNavigation<CustomAvatarScreenNavigationProp>();
  const { width } = useWindowDimensions();

  // Avatar customization state
  const [faceIndex, setFaceIndex] = useState(0);
  const [eyesIndex, setEyesIndex] = useState(0);
  const [mouthIndex, setMouthIndex] = useState(0);
  const [colorIndex, setColorIndex] = useState(0);

  // Animation intensity
  const [animationIntensity, setAnimationIntensity] = useState(1);

  // Handle navigation back
  const handleGoBack = () => {
    navigation.goBack();
  };

  // Calculate avatar size based on screen width
  const avatarSize = Math.min(width * 0.6, 240);

  // Helper function to create selector rows with arrows
  const renderSelector = (
    label: string,
    currentIndex: number,
    setIndex: (index: number) => void,
    maxIndex: number
  ) => (
    <View style={styles.selectorRow}>
      <TouchableOpacity
        style={[
          styles.arrowButton,
          {
            backgroundColor: theme.backgroundAlt,
            borderRadius: borderRadius.round,
            ...applyThemeShadow('sm')
          }
        ]}
        onPress={() => setIndex((currentIndex - 1 + maxIndex) % maxIndex)}
      >
        <Ionicons name="chevron-back" size={24} color={theme.text} />
      </TouchableOpacity>

      <Text
        variant="subtitle"
        size={typography.fontSizes.md}
        style={{ minWidth: 80, textAlign: 'center' }}
      >
        {label}
      </Text>

      <TouchableOpacity
        style={[
          styles.arrowButton,
          {
            backgroundColor: theme.backgroundAlt,
            borderRadius: borderRadius.round,
            ...applyThemeShadow('sm')
          }
        ]}
        onPress={() => setIndex((currentIndex + 1) % maxIndex)}
      >
        <Ionicons name="chevron-forward" size={24} color={theme.text} />
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaContainer style={styles.container} edges={['top', 'bottom']}>
      <View style={[styles.header, { paddingHorizontal: spacing.lg, paddingVertical: spacing.md }]}>
        <TouchableOpacity
          style={[
            styles.backButton,
            {
              backgroundColor: theme.backgroundAlt,
              borderRadius: borderRadius.round / 2,
              ...applyThemeShadow('sm'),
            },
          ]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>

        <Text variant="heading" size={typography.fontSizes.xl}>
          Pixel Avatar
        </Text>

        <View style={{ width: 44 }} />
      </View>

      <View style={[styles.content, { padding: spacing.lg }]}>
        {/* Avatar Preview with blue background like skribbl.io */}
        <View
          style={[
            styles.avatarPreview,
            {
              marginBottom: spacing.xl,
              backgroundColor: '#1F3A93', // skribbl.io blue background
              borderRadius: borderRadius.md,
              padding: spacing.lg,
              ...applyThemeShadow('md')
            }
          ]}
        >
          <PixelAvatarV2
            size={avatarSize}
            faceIndex={faceIndex}
            eyesIndex={eyesIndex}
            mouthIndex={mouthIndex}
            colorIndex={colorIndex}
            animationIntensity={animationIntensity}
          />
        </View>

        {/* Skribbl.io style selectors with arrows */}
        <View style={styles.selectors}>
          {/* Face Shape Selector */}
          {renderSelector('Face', faceIndex, setFaceIndex, 3)}

          {/* Eyes Selector */}
          {renderSelector('Eyes', eyesIndex, setEyesIndex, 5)}

          {/* Mouth Selector */}
          {renderSelector('Mouth', mouthIndex, setMouthIndex, 5)}

          {/* Color Selector */}
          {renderSelector('Color', colorIndex, setColorIndex, 8)}

          {/* Animation Intensity */}
          <View style={[styles.selectorRow, { marginTop: spacing.md }]}>
            <Text
              variant="subtitle"
              size={typography.fontSizes.md}
              style={{ marginRight: spacing.md }}
            >
              Jiggle:
            </Text>

            <View style={styles.intensityControls}>
              <TouchableOpacity
                style={[
                  styles.intensityButton,
                  {
                    backgroundColor: theme.backgroundAlt,
                    borderRadius: borderRadius.sm,
                    ...applyThemeShadow('sm')
                  }
                ]}
                onPress={() => setAnimationIntensity(Math.max(0, animationIntensity - 0.5))}
              >
                <Ionicons name="remove" size={16} color={theme.text} />
              </TouchableOpacity>

              <Text
                variant="body"
                style={{ minWidth: 40, textAlign: 'center' }}
              >
                {animationIntensity.toFixed(1)}
              </Text>

              <TouchableOpacity
                style={[
                  styles.intensityButton,
                  {
                    backgroundColor: theme.backgroundAlt,
                    borderRadius: borderRadius.sm,
                    ...applyThemeShadow('sm')
                  }
                ]}
                onPress={() => setAnimationIntensity(Math.min(2, animationIntensity + 0.5))}
              >
                <Ionicons name="add" size={16} color={theme.text} />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaContainer>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    alignItems: 'center',
  },
  avatarPreview: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    maxWidth: 300,
  },
  selectors: {
    width: '100%',
    maxWidth: 300,
  },
  selectorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingHorizontal: 10,
  },
  arrowButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  intensityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  intensityButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CustomAvatarScreen;
