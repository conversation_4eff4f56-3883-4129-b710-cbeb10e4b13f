# Custom Avatars

This folder contains custom avatar images for the Amazonian app.

## Instructions

1. Add your custom avatar images here with the following naming convention:
   - `avatar_cat.png`
   - `avatar_dog.png`
   - `avatar_robot.png`
   - `avatar_alien.png`
   - `avatar_wizard.png`
   - `avatar_ninja.png`
   - `avatar_pirate.png`
   - `avatar_knight.png`

2. **Image Specifications:**
   - Format: PNG, JPG, or GIF (GIFs will animate automatically!)
   - Size: 128x128px or 256x256px (square)
   - Background: Transparent or solid color
   - Style: Consistent with your app's design
   - GIF Notes: Keep file size reasonable (<500KB) for smooth performance

3. **Current Status:**
   - ✅ `avatar_pirate.gif` - Added and configured (animated GIF)
   - The ProfileSetupTestScreen is configured to use these custom images with fallback to Ionicons if images are missing.

4. **Adding More Avatars:**
   To add more avatars, simply:
   - Add the image file to this folder
   - Update the `avatarOptions` array in `ProfileSetupTestScreen.tsx`
   - Add the require statement for your new image

## Example Avatar Options Structure

```javascript
const avatarOptions = [
  { id: '1', name: 'Cat', image: require('../../../assets/avatars/avatar_cat.png') },
  { id: '2', name: 'Dog', image: require('../../../assets/avatars/avatar_dog.png') },
  // Add more as needed...
];
```
