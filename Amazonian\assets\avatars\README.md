# Custom Avatars

This folder contains custom avatar images for the Amazonian app.

## Instructions

1. Add your custom avatar images here with the following naming convention:
   - `avatar_cat.png`
   - `avatar_dog.png` 
   - `avatar_robot.png`
   - `avatar_alien.png`
   - `avatar_wizard.png`
   - `avatar_ninja.png`
   - `avatar_pirate.png`
   - `avatar_knight.png`

2. **Image Specifications:**
   - Format: PNG with transparency support
   - Size: 128x128px or 256x256px (square)
   - Background: Transparent or solid color
   - Style: Consistent with your app's design

3. **Current Status:**
   The ProfileSetupTestScreen is configured to use these custom images with fallback to Ionicons if images are missing.

4. **Adding More Avatars:**
   To add more avatars, simply:
   - Add the image file to this folder
   - Update the `avatarOptions` array in `ProfileSetupTestScreen.tsx`
   - Add the require statement for your new image

## Example Avatar Options Structure

```javascript
const avatarOptions = [
  { id: '1', name: 'Cat', image: require('../../../assets/avatars/avatar_cat.png') },
  { id: '2', name: '<PERSON>', image: require('../../../assets/avatars/avatar_dog.png') },
  // Add more as needed...
];
```
