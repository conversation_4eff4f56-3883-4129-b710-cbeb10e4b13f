{"name": "Amazonian", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start --offline", "android": "expo start --offline --android", "ios": "expo start --offline --ios", "web": "expo start --offline --web"}, "dependencies": {"@expo-google-fonts/nunito": "^0.3.0", "@expo-google-fonts/patrick-hand": "^0.3.0", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@react-navigation/stack": "^7.3.2", "@shopify/react-native-skia": "^2.0.0-next.4", "@supabase/supabase-js": "^2.49.4", "@types/uuid": "^8.3.4", "expo": "~53.0.9", "expo-font": "^13.3.1", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.25.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-reanimated": "^3.6.2", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.12.0", "react-native-webview": "^13.13.5", "uuid": "^8.3.2", "zustand": "^4.5.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.5.3", "typescript": "~5.8.3"}, "private": true}